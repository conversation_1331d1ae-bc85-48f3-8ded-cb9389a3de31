<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-geo-alt me-2"></i>Districts Management
                                <?php if (isset($selectedProvince)): ?>
                                    <small class="text-muted">in <?= esc($selectedProvince['name']) ?>, <?= esc($selectedProvince['country_name']) ?></small>
                                <?php endif; ?>
                            </h2>
                            <p class="text-light mb-0">Manage geographic districts and their information</p>
                        </div>
                        <div>
                            <?php if (isset($selectedProvinceId)): ?>
                                <a href="<?= base_url('dakoii/geographic/districts/new/' . $selectedProvinceId) ?>" class="btn btn-primary me-2">
                                    <i class="bi bi-geo-alt-fill me-2"></i>Add District to <?= esc($selectedProvince['name']) ?>
                                </a>
                                <div class="btn-group me-2" role="group">
                                    <a href="<?= base_url('dakoii/geographic/districts/import?province_id=' . $selectedProvinceId) ?>" class="btn btn-success">
                                        <i class="bi bi-upload me-2"></i>Import CSV
                                    </a>
                                    <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item text-dark" href="<?= base_url('dakoii/geographic/districts/download-template?type=sample') ?>">
                                            <i class="bi bi-download me-2"></i>Download Sample Template
                                        </a></li>
                                        <li><a class="dropdown-item text-dark" href="<?= base_url('dakoii/geographic/districts/download-template?type=blank') ?>">
                                            <i class="bi bi-file-earmark me-2"></i>Download Blank Template
                                        </a></li>
                                    </ul>
                                </div>
                                <a href="<?= base_url('dakoii/geographic/provinces/' . $selectedProvinceId) ?>" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Province
                                </a>
                            <?php else: ?>
                                <a href="<?= base_url('dakoii/geographic/districts/new') ?>" class="btn btn-primary me-2">
                                    <i class="bi bi-geo-alt-fill me-2"></i>Add New District
                                </a>
                                <div class="btn-group" role="group">
                                    <a href="<?= base_url('dakoii/geographic/districts/import') ?>" class="btn btn-success">
                                        <i class="bi bi-upload me-2"></i>Import CSV
                                    </a>
                                    <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item text-dark" href="<?= base_url('dakoii/geographic/districts/download-template?type=sample') ?>">
                                            <i class="bi bi-download me-2"></i>Download Sample Template
                                        </a></li>
                                        <li><a class="dropdown-item text-dark" href="<?= base_url('dakoii/geographic/districts/download-template?type=blank') ?>">
                                            <i class="bi bi-file-earmark me-2"></i>Download Blank Template
                                        </a></li>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i><?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('import_errors')): ?>
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <h6><i class="bi bi-exclamation-triangle me-2"></i>Import Errors:</h6>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('import_errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Country Filter (only show if not filtered by province) -->
    <?php if (!isset($selectedProvinceId)): ?>
        <div class="row mb-3">
            <div class="col-12">
                <div class="card card-dark">
                    <div class="card-body">
                        <form method="get" action="<?= base_url('dakoii/geographic/districts') ?>">
                            <div class="row align-items-end">
                                <div class="col-md-4">
                                    <label for="country_filter" class="form-label text-light">Filter by Country</label>
                                    <select class="form-select" id="country_filter" name="country_id" onchange="this.form.submit()">
                                        <option value="">All Countries</option>
                                        <?php foreach ($countries as $id => $name): ?>
                                            <option value="<?= $id ?>" <?= ($selectedCountryId == $id) ? 'selected' : '' ?>>
                                                <?= esc($name) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Districts Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-list me-2"></i>Districts List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($districts)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-geo-alt text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Districts Found</h4>
                            <?php if (isset($selectedProvinceId)): ?>
                                <p class="text-muted">Start by adding districts to <?= esc($selectedProvince['name']) ?>.</p>
                                <a href="<?= base_url('dakoii/geographic/districts/new/' . $selectedProvinceId) ?>" class="btn btn-primary">
                                    <i class="bi bi-geo-alt-fill me-2"></i>Add District
                                </a>
                            <?php else: ?>
                                <p class="text-muted">Start by adding your first district.</p>
                                <a href="<?= base_url('dakoii/geographic/districts/new') ?>" class="btn btn-primary">
                                    <i class="bi bi-geo-alt-fill me-2"></i>Add District
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>District</th>
                                        <th>District Code</th>
                                        <?php if (!isset($selectedProvinceId)): ?>
                                            <th>Province</th>
                                            <th>Country</th>
                                        <?php endif; ?>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($districts as $district): ?>
                                        <tr>
                                            <td><?= esc($district['id']) ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-warning rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="bi bi-geo-alt text-white"></i>
                                                    </div>
                                                    <div>
                                                        <strong><?= esc($district['name']) ?></strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($district['district_code']): ?>
                                                    <span class="badge bg-warning"><?= esc($district['district_code']) ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">No code</span>
                                                <?php endif; ?>
                                            </td>
                                            <?php if (!isset($selectedProvinceId)): ?>
                                                <td>
                                                    <a href="<?= base_url('dakoii/geographic/districts/province/' . $district['province_id']) ?>" class="text-success">
                                                        <?= esc($district['province_name'] ?? 'Unknown') ?>
                                                    </a>
                                                </td>
                                                <td>
                                                    <a href="<?= base_url('dakoii/geographic/countries/' . $district['country_id']) ?>" class="text-info">
                                                        <?= esc($district['country_name'] ?? 'Unknown') ?>
                                                    </a>
                                                </td>
                                            <?php endif; ?>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M j, Y', strtotime($district['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('dakoii/geographic/districts/' . $district['id']) ?>" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('dakoii/geographic/districts/' . $district['id'] . '/edit') ?>" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(<?= $district['id'] ?>, '<?= esc($district['name']) ?>', <?= $district['province_id'] ?>)" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title text-light">Confirm Delete</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <p>Are you sure you want to delete the district <strong id="districtName"></strong>?</p>
                <p class="text-warning"><i class="bi bi-exclamation-triangle me-2"></i>This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">Delete District</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(districtId, districtName, provinceId) {
    document.getElementById('districtName').textContent = districtName;
    document.getElementById('deleteForm').action = '<?= base_url('dakoii/geographic/districts/') ?>' + districtId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
<?= $this->endSection() ?>
