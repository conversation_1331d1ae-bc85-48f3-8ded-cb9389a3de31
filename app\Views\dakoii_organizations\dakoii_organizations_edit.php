<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-pencil me-2"></i>Edit Organization
                            </h2>
                            <p class="text-light mb-0">Update organization: <strong><?= esc($organization['org_name']) ?></strong></p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-10 col-md-12 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-building me-2"></i>Organization Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?= base_url('dakoii/organizations/' . $organization['id'] . '/update') ?>" enctype="multipart/form-data">
                        <?= csrf_field() ?>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="org_code" class="form-label text-light">Organization Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="org_code" name="org_code" 
                                       value="<?= old('org_code', $organization['org_code']) ?>" required>
                                <div class="form-text text-muted">Unique identifier for the organization</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="org_name" class="form-label text-light">Organization Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="org_name" name="org_name" 
                                       value="<?= old('org_name', $organization['org_name']) ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="description" class="form-label text-light">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?= old('description', $organization['description']) ?></textarea>
                                <div class="form-text text-muted">Brief description of the organization</div>
                            </div>
                        </div>

                        <!-- Logo Upload -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="logo" class="form-label text-light">Organization Logo</label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                <div class="form-text text-muted">Upload organization logo (optional). Accepted formats: JPG, PNG, GIF. Max size: 2MB</div>
                                <?php if (!empty($organization['logo_path'])): ?>
                                    <div class="mt-2">
                                        <small class="text-muted">Current logo: <?= esc(basename($organization['logo_path'])) ?></small>
                                        <div class="mt-1">
                                            <img src="<?= base_url($organization['logo_path']) ?>" alt="Current Logo" class="img-thumbnail" style="max-height: 100px;">
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <h6 class="text-light mb-3 mt-4">Location Information</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country_id" class="form-label text-light">Country</label>
                                <select class="form-select" id="country_id" name="country_id">
                                    <option value="">Select Country</option>
                                    <?php foreach ($countries as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= old('country_id', $organization['country_id']) == $id ? 'selected' : '' ?>><?= esc($name) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="province_id" class="form-label text-light">Province/State</label>
                                <select class="form-select" id="province_id" name="province_id">
                                    <option value="">Select Province/State</option>
                                    <?php foreach ($provinces as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= old('province_id', $organization['province_id']) == $id ? 'selected' : '' ?>><?= esc($name) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="postal_address" class="form-label text-light">Postal Address</label>
                                <textarea class="form-control" id="postal_address" name="postal_address" rows="3"><?= old('postal_address', $organization['postal_address']) ?></textarea>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <h6 class="text-light mb-3 mt-4">Contact Information</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone_numbers" class="form-label text-light">Phone Numbers</label>
                                <input type="text" class="form-control" id="phone_numbers" name="phone_numbers" 
                                       value="<?= old('phone_numbers', $organization['phone_numbers']) ?>">
                                <div class="form-text text-muted">Separate multiple numbers with commas</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email_addresses" class="form-label text-light">Email Address</label>
                                <input type="email" class="form-control" id="email_addresses" name="email_addresses" 
                                       value="<?= old('email_addresses', $organization['email_addresses']) ?>">
                            </div>
                        </div>

                        <!-- Settings -->
                        <h6 class="text-light mb-3 mt-4">Settings</h6>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="license_status" class="form-label text-light">License Status</label>
                                <select class="form-select" id="license_status" name="license_status">
                                    <option value="">Select Status</option>
                                    <option value="active" <?= old('license_status', $organization['license_status']) == 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="trial" <?= old('license_status', $organization['license_status']) == 'trial' ? 'selected' : '' ?>>Trial</option>
                                    <option value="expired" <?= old('license_status', $organization['license_status']) == 'expired' ? 'selected' : '' ?>>Expired</option>
                                    <option value="suspended" <?= old('license_status', $organization['license_status']) == 'suspended' ? 'selected' : '' ?>>Suspended</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="is_active" class="form-label text-light">Organization Status</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="1" <?= old('is_active', $organization['is_active']) == '1' ? 'selected' : '' ?>>Active</option>
                                    <option value="0" <?= old('is_active', $organization['is_active']) == '0' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="is_locationlocked" class="form-label text-light">Location Lock</label>
                                <select class="form-select" id="is_locationlocked" name="is_locationlocked">
                                    <option value="0" <?= old('is_locationlocked', $organization['is_locationlocked']) == '0' ? 'selected' : '' ?>>Not Locked</option>
                                    <option value="1" <?= old('is_locationlocked', $organization['is_locationlocked']) == '1' ? 'selected' : '' ?>>Location Locked</option>
                                </select>
                                <div class="form-text text-muted">Restrict organization to specific location</div>
                            </div>
                        </div>

                        <!-- Audit Information -->
                        <div class="row mb-3 mt-4">
                            <div class="col-12">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-body">
                                        <h6 class="text-light">Organization Information</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <small class="text-muted">Created: <?= date('M d, Y H:i', strtotime($organization['created_at'])) ?></small>
                                            </div>
                                            <div class="col-md-6">
                                                <small class="text-muted">Last Updated: <?= $organization['updated_at'] ? date('M d, Y H:i', strtotime($organization['updated_at'])) : 'Never' ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Update Organization
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const countrySelect = document.getElementById('country_id');
    const provinceSelect = document.getElementById('province_id');
    
    countrySelect.addEventListener('change', function() {
        const countryId = this.value;
        
        // Clear province options except the first one
        provinceSelect.innerHTML = '<option value="">Select Province/State</option>';
        
        if (countryId) {
            // Fetch provinces for selected country
            fetch(`<?= base_url('dakoii/organizations/provinces/') ?>${countryId}`)
                .then(response => response.json())
                .then(data => {
                    Object.entries(data).forEach(([id, name]) => {
                        const option = document.createElement('option');
                        option.value = id;
                        option.textContent = name;
                        provinceSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error fetching provinces:', error));
        }
    });
});
</script>
<?= $this->endSection() ?>
