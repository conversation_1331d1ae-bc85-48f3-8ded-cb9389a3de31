<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-upload me-2"></i>Import Districts from CSV
                                <?php if (isset($selectedProvince)): ?>
                                    <small class="text-muted">to <?= esc($selectedProvince['name']) ?>, <?= esc($selectedProvince['country_name']) ?></small>
                                <?php endif; ?>
                            </h2>
                            <p class="text-light mb-0">Bulk import districts from a CSV file</p>
                        </div>
                        <div>
                            <?php if (isset($selectedProvinceId)): ?>
                                <a href="<?= base_url('dakoii/geographic/districts/province/' . $selectedProvinceId) ?>" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to <?= esc($selectedProvince['name']) ?> Districts
                                </a>
                            <?php else: ?>
                                <a href="<?= base_url('dakoii/geographic/districts') ?>" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Districts
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-file-earmark-spreadsheet me-2"></i>CSV Import
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i><?= session()->getFlashdata('error') ?>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?= base_url('dakoii/geographic/districts/process-import') ?>" enctype="multipart/form-data">
                        <?= csrf_field() ?>
                        
                        <!-- Country and Province Selection -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country_id" class="form-label text-light">Target Country <span class="text-danger">*</span></label>
                                <select class="form-select" id="country_id" name="country_id" required onchange="loadProvinces()" <?= isset($selectedCountryId) ? 'disabled' : '' ?>>
                                    <option value="">Select Country</option>
                                    <?php foreach ($countries as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= (isset($selectedCountryId) && $selectedCountryId == $id) ? 'selected' : '' ?>>
                                            <?= esc($name) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text text-muted">Select the country</div>
                                <?php if (isset($selectedCountryId)): ?>
                                    <input type="hidden" name="country_id" value="<?= $selectedCountryId ?>">
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="province_id" class="form-label text-light">Target Province <span class="text-danger">*</span></label>
                                <select class="form-select" id="province_id" name="province_id" required <?= isset($selectedProvinceId) ? 'disabled' : '' ?>>
                                    <option value="">Select Province</option>
                                    <?php foreach ($provinces as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= (isset($selectedProvinceId) && $selectedProvinceId == $id) ? 'selected' : '' ?>>
                                            <?= esc($name) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text text-muted">All districts in the CSV will be assigned to this province</div>
                                <?php if (isset($selectedProvinceId)): ?>
                                    <input type="hidden" name="province_id" value="<?= $selectedProvinceId ?>">
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- File Upload -->
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="csv_file" class="form-label text-light">CSV File <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                <div class="form-text text-muted">Select a CSV file containing district data</div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <?php if (isset($selectedProvinceId)): ?>
                                <a href="<?= base_url('dakoii/geographic/districts/province/' . $selectedProvinceId) ?>" class="btn btn-secondary">
                                    <i class="bi bi-x-circle me-2"></i>Cancel
                                </a>
                            <?php else: ?>
                                <a href="<?= base_url('dakoii/geographic/districts') ?>" class="btn btn-secondary">
                                    <i class="bi bi-x-circle me-2"></i>Cancel
                                </a>
                            <?php endif; ?>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-upload me-2"></i>Import Districts
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- CSV Format Instructions -->
    <div class="row mt-4">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-info-circle me-2"></i>CSV Format Requirements
                    </h5>
                </div>
                <div class="card-body">
                    <h6 class="text-light">Required Columns:</h6>
                    <ul class="text-light">
                        <li><strong>district_code</strong> - Optional district code (can be empty)</li>
                        <li><strong>name</strong> - Full district name (e.g., Port Moresby, Lae Urban)</li>
                    </ul>

                    <h6 class="text-light mt-3">Sample CSV Format:</h6>
                    <div class="bg-secondary p-3 rounded">
                        <code class="text-light">
                            district_code,name<br>
                            POM,Port Moresby<br>
                            LAE,Lae Urban<br>
                            ,Mount Hagen<br>
                            VAN,Vanimo
                        </code>
                    </div>

                    <div class="alert alert-info mt-3">
                        <h6><i class="bi bi-lightbulb me-2"></i>Tips:</h6>
                        <ul class="mb-0">
                            <li>Ensure the first row contains the column headers exactly as shown</li>
                            <li>District codes are optional - leave empty if not available</li>
                            <li>District codes will be automatically converted to uppercase</li>
                            <li>Empty rows will be skipped</li>
                            <li>All districts will be assigned to the selected province and country</li>
                            <li>If errors occur, they will be displayed after import</li>
                        </ul>
                    </div>

                    <div class="text-center mt-3">
                        <a href="<?= base_url('dakoii/geographic/districts/download-template?type=sample') ?>"
                           class="btn btn-outline-info btn-sm me-2 text-info">
                            <i class="bi bi-download me-2"></i>Download CSV Template
                        </a>
                        <a href="<?= base_url('dakoii/geographic/districts/download-template?type=blank') ?>"
                           class="btn btn-outline-secondary btn-sm text-secondary">
                            <i class="bi bi-file-earmark me-2"></i>Download Blank Template
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function loadProvinces() {
    const countryId = document.getElementById('country_id').value;
    const provinceSelect = document.getElementById('province_id');
    
    // Clear current options
    provinceSelect.innerHTML = '<option value="">Loading...</option>';
    
    if (!countryId) {
        provinceSelect.innerHTML = '<option value="">Select Province</option>';
        return;
    }
    
    // Make AJAX request to load provinces
    fetch('<?= base_url('dakoii/geographic/districts/get-provinces') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'country_id=' + countryId + '&<?= csrf_token() ?>=' + '<?= csrf_hash() ?>'
    })
    .then(response => response.json())
    .then(data => {
        provinceSelect.innerHTML = '<option value="">Select Province</option>';
        
        if (data.provinces) {
            Object.keys(data.provinces).forEach(id => {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = data.provinces[id];
                provinceSelect.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading provinces:', error);
        provinceSelect.innerHTML = '<option value="">Error loading provinces</option>';
    });
}
</script>
<?= $this->endSection() ?>
