<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-flag me-2"></i>Countries Management
                            </h2>
                            <p class="text-light mb-0">Manage geographic countries and their information</p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/geographic/countries/new') ?>" class="btn btn-primary">
                                <i class="bi bi-flag-fill me-2"></i>Add New Country
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i><?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Countries Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-list me-2"></i>Countries List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($countries)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-flag text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Countries Found</h4>
                            <p class="text-muted">Start by adding your first country.</p>
                            <a href="<?= base_url('dakoii/geographic/countries/new') ?>" class="btn btn-primary">
                                <i class="bi bi-flag-fill me-2"></i>Add Country
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Country</th>
                                        <th>Country Code</th>
                                        <th>Provinces</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($countries as $country): ?>
                                        <tr>
                                            <td><?= esc($country['id']) ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="bi bi-flag text-white"></i>
                                                    </div>
                                                    <div>
                                                        <strong><?= esc($country['name']) ?></strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= esc($country['country_code']) ?></span>
                                            </td>
                                            <td>
                                                <?php if (isset($country['province_count']) && $country['province_count'] > 0): ?>
                                                    <a href="<?= base_url('dakoii/geographic/provinces/country/' . $country['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-map me-1"></i><?= $country['province_count'] ?> Provinces
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">No provinces</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M j, Y', strtotime($country['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('dakoii/geographic/countries/' . $country['id']) ?>" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('dakoii/geographic/countries/' . $country['id'] . '/edit') ?>" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(<?= $country['id'] ?>, '<?= esc($country['name']) ?>')" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title text-light">Confirm Delete</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <p>Are you sure you want to delete the country <strong id="countryName"></strong>?</p>
                <p class="text-warning"><i class="bi bi-exclamation-triangle me-2"></i>This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">Delete Country</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(countryId, countryName) {
    document.getElementById('countryName').textContent = countryName;
    document.getElementById('deleteForm').action = '<?= base_url('dakoii/geographic/countries/') ?>' + countryId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
<?= $this->endSection() ?>
