<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-flag-fill me-2"></i>Add New Country
                            </h2>
                            <p class="text-light mb-0">Create a new geographic country</p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/geographic/countries') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-flag me-2"></i>Country Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?= base_url('dakoii/geographic/countries/create') ?>">
                        <?= csrf_field() ?>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="name" class="form-label text-light">Country Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name') ?>" required maxlength="100">
                                <div class="form-text text-muted">Full name of the country</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="country_code" class="form-label text-light">Country Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="country_code" name="country_code" 
                                       value="<?= old('country_code') ?>" required maxlength="2" 
                                       style="text-transform: uppercase;" placeholder="e.g., PG">
                                <div class="form-text text-muted">2-letter ISO country code</div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="<?= base_url('dakoii/geographic/countries') ?>" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Create Country
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-uppercase country code input
    const countryCodeInput = document.getElementById('country_code');
    countryCodeInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });
});
</script>
<?= $this->endSection() ?>
