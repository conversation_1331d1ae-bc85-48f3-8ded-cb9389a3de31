<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-flag me-2"></i>Country Details
                            </h2>
                            <p class="text-light mb-0">View country information</p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/geographic/countries') ?>" class="btn btn-secondary me-2">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                            <a href="<?= base_url('dakoii/geographic/countries/' . $country['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit Country
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Country Details -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-info-circle me-2"></i>Country Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8 mb-4">
                            <label class="form-label text-light">Country Name</label>
                            <div class="form-control-plaintext text-light fs-5">
                                <i class="bi bi-flag me-2"></i><?= esc($country['name']) ?>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <label class="form-label text-light">Country Code</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-info fs-6"><?= esc($country['country_code']) ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-light">Created</label>
                            <div class="form-control-plaintext text-muted">
                                <i class="bi bi-calendar-plus me-2"></i>
                                <?= date('F j, Y g:i A', strtotime($country['created_at'])) ?>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-light">Last Updated</label>
                            <div class="form-control-plaintext text-muted">
                                <i class="bi bi-calendar-check me-2"></i>
                                <?= date('F j, Y g:i A', strtotime($country['updated_at'])) ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Data -->
    <div class="row mt-4">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-map me-2"></i>Related Geographic Data
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-6">
                            <div class="p-3 border rounded">
                                <i class="bi bi-map text-primary" style="font-size: 2rem;"></i>
                                <h6 class="text-light mt-2">Provinces</h6>
                                <p class="text-muted mb-2">Manage provinces within this country</p>
                                <a href="<?= base_url('dakoii/geographic/provinces/country/' . $country['id']) ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye me-1"></i>View Provinces
                                </a>
                                <a href="<?= base_url('dakoii/geographic/provinces/new/' . $country['id']) ?>" class="btn btn-primary btn-sm">
                                    <i class="bi bi-plus me-1"></i>Add Province
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="p-3 border rounded">
                                <i class="bi bi-geo-alt text-success" style="font-size: 2rem;"></i>
                                <h6 class="text-light mt-2">Districts</h6>
                                <p class="text-muted mb-2">View all districts in this country</p>
                                <a href="<?= base_url('dakoii/geographic/districts?country_id=' . $country['id']) ?>" class="btn btn-outline-success btn-sm">
                                    <i class="bi bi-eye me-1"></i>View Districts
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
