<?php

namespace App\Controllers;

use App\Models\GeoCountryModel;
use CodeIgniter\Controller;

class DakoiiGeographic extends Controller
{
    protected $geoCountryModel;
    protected $session;

    public function __construct()
    {
        $this->geoCountryModel = new GeoCountryModel();
        $this->session = session();
    }

    /**
     * Check authentication
     */
    private function checkAuth()
    {
        if (!$this->session->get('dakoii_logged_in')) {
            return redirect()->to(base_url('dakoii'))->with('error', 'Please login to access this page.');
        }
        return null;
    }

    /**
     * Display list of countries
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $countries = $this->geoCountryModel->getCountriesWithProvinceCount();

        $data = [
            'title' => 'Countries Management',
            'countries' => $countries
        ];

        return view('dakoii_geographic/dakoii_geographic_country_list', $data);
    }

    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $data = [
            'title' => 'Add New Country',
            'country' => []
        ];

        return view('dakoii_geographic/dakoii_geographic_country_create', $data);
    }

    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $data = [
            'name' => $this->request->getPost('name'),
            'country_code' => strtoupper($this->request->getPost('country_code')),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->geoCountryModel->insert($data)) {
            return redirect()->to(base_url('dakoii/geographic/countries'))
                           ->with('success', 'Country created successfully.');
        } else {
            return redirect()->back()
                           ->withInput()
                           ->with('errors', $this->geoCountryModel->errors());
        }
    }

    /**
     * Show country details
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $country = $this->geoCountryModel->find($id);
        
        if (!$country) {
            return redirect()->to(base_url('dakoii/geographic/countries'))
                           ->with('error', 'Country not found.');
        }

        $data = [
            'title' => 'Country Details',
            'country' => $country
        ];

        return view('dakoii_geographic/dakoii_geographic_country_show', $data);
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $country = $this->geoCountryModel->find($id);
        
        if (!$country) {
            return redirect()->to(base_url('dakoii/geographic/countries'))
                           ->with('error', 'Country not found.');
        }

        $data = [
            'title' => 'Edit Country',
            'country' => $country
        ];

        return view('dakoii_geographic/dakoii_geographic_country_edit', $data);
    }

    /**
     * Handle update form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $country = $this->geoCountryModel->find($id);
        
        if (!$country) {
            return redirect()->to(base_url('dakoii/geographic/countries'))
                           ->with('error', 'Country not found.');
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'country_code' => strtoupper($this->request->getPost('country_code')),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->geoCountryModel->update($id, $data)) {
            return redirect()->to(base_url('dakoii/geographic/countries'))
                           ->with('success', 'Country updated successfully.');
        } else {
            return redirect()->back()
                           ->withInput()
                           ->with('errors', $this->geoCountryModel->errors());
        }
    }

    /**
     * Handle delete
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $country = $this->geoCountryModel->find($id);
        
        if (!$country) {
            return redirect()->to(base_url('dakoii/geographic/countries'))
                           ->with('error', 'Country not found.');
        }

        // Check if country has provinces
        $provinceModel = new \App\Models\GeoProvinceModel();
        $provinceCount = $provinceModel->where('country_id', $id)->countAllResults();
        
        if ($provinceCount > 0) {
            return redirect()->to(base_url('dakoii/geographic/countries'))
                           ->with('error', 'Cannot delete country. It has ' . $provinceCount . ' provinces associated with it.');
        }

        if ($this->geoCountryModel->delete($id)) {
            return redirect()->to(base_url('dakoii/geographic/countries'))
                           ->with('success', 'Country deleted successfully.');
        } else {
            return redirect()->to(base_url('dakoii/geographic/countries'))
                           ->with('error', 'Failed to delete country.');
        }
    }
}
