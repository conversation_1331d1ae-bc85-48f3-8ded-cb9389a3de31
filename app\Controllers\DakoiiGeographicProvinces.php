<?php

namespace App\Controllers;

use App\Models\GeoProvinceModel;
use App\Models\GeoCountryModel;
use CodeIgniter\Controller;

class DakoiiGeographicProvinces extends Controller
{
    protected $geoProvinceModel;
    protected $geoCountryModel;
    protected $session;

    public function __construct()
    {
        $this->geoProvinceModel = new GeoProvinceModel();
        $this->geoCountryModel = new GeoCountryModel();
        $this->session = session();
    }

    /**
     * Check authentication
     */
    private function checkAuth()
    {
        if (!$this->session->get('dakoii_logged_in')) {
            return redirect()->to(base_url('dakoii'))->with('error', 'Please login to access this page.');
        }
        return null;
    }

    /**
     * Display list of provinces
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $provinces = $this->geoProvinceModel->getProvincesWithCountry();
        $countries = $this->geoCountryModel->getCountriesForDropdown();

        $data = [
            'title' => 'Provinces Management',
            'provinces' => $provinces,
            'countries' => $countries
        ];

        return view('dakoii_geographic/dakoii_geographic_province_list', $data);
    }

    /**
     * Display provinces by country
     */
    public function byCountry($countryId)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $country = $this->geoCountryModel->find($countryId);
        if (!$country) {
            return redirect()->to(base_url('dakoii/geographic/provinces'))
                           ->with('error', 'Country not found.');
        }

        $provinces = $this->geoProvinceModel->getByCountry($countryId);
        $countries = $this->geoCountryModel->getCountriesForDropdown();

        $data = [
            'title' => 'Provinces in ' . $country['name'],
            'provinces' => $provinces,
            'countries' => $countries,
            'selectedCountry' => $country,
            'selectedCountryId' => $countryId
        ];

        return view('dakoii_geographic/dakoii_geographic_province_list', $data);
    }

    /**
     * Show create form
     */
    public function new($countryId = null)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $countries = $this->geoCountryModel->getCountriesForDropdown();
        $selectedCountry = null;

        if ($countryId) {
            $selectedCountry = $this->geoCountryModel->find($countryId);
            if (!$selectedCountry) {
                return redirect()->to(base_url('dakoii/geographic/provinces/new'))
                               ->with('error', 'Country not found.');
            }
        }

        $data = [
            'title' => 'Add New Province',
            'province' => [],
            'countries' => $countries,
            'selectedCountryId' => $countryId,
            'selectedCountry' => $selectedCountry
        ];

        return view('dakoii_geographic/dakoii_geographic_province_create', $data);
    }

    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $data = [
            'province_code' => strtoupper($this->request->getPost('province_code')),
            'name' => $this->request->getPost('name'),
            'country_id' => $this->request->getPost('country_id'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->geoProvinceModel->insert($data)) {
            $countryId = $data['country_id'];
            return redirect()->to(base_url('dakoii/geographic/provinces/country/' . $countryId))
                           ->with('success', 'Province created successfully.');
        } else {
            return redirect()->back()
                           ->withInput()
                           ->with('errors', $this->geoProvinceModel->errors());
        }
    }

    /**
     * Show province details
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $province = $this->geoProvinceModel->select('geo_provinces.*, geo_countries.name as country_name, geo_countries.country_code')
                                          ->join('geo_countries', 'geo_countries.id = geo_provinces.country_id', 'left')
                                          ->where('geo_provinces.id', $id)
                                          ->first();
        
        if (!$province) {
            return redirect()->to(base_url('dakoii/geographic/provinces'))
                           ->with('error', 'Province not found.');
        }

        $data = [
            'title' => 'Province Details',
            'province' => $province
        ];

        return view('dakoii_geographic/dakoii_geographic_province_show', $data);
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $province = $this->geoProvinceModel->find($id);
        
        if (!$province) {
            return redirect()->to(base_url('dakoii/geographic/provinces'))
                           ->with('error', 'Province not found.');
        }

        $countries = $this->geoCountryModel->getCountriesForDropdown();

        $data = [
            'title' => 'Edit Province',
            'province' => $province,
            'countries' => $countries
        ];

        return view('dakoii_geographic/dakoii_geographic_province_edit', $data);
    }

    /**
     * Handle update form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $province = $this->geoProvinceModel->find($id);
        
        if (!$province) {
            return redirect()->to(base_url('dakoii/geographic/provinces'))
                           ->with('error', 'Province not found.');
        }

        $data = [
            'province_code' => strtoupper($this->request->getPost('province_code')),
            'name' => $this->request->getPost('name'),
            'country_id' => $this->request->getPost('country_id'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->geoProvinceModel->update($id, $data)) {
            return redirect()->to(base_url('dakoii/geographic/provinces/country/' . $data['country_id']))
                           ->with('success', 'Province updated successfully.');
        } else {
            return redirect()->back()
                           ->withInput()
                           ->with('errors', $this->geoProvinceModel->errors());
        }
    }

    /**
     * Handle delete
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $province = $this->geoProvinceModel->find($id);
        
        if (!$province) {
            return redirect()->to(base_url('dakoii/geographic/provinces'))
                           ->with('error', 'Province not found.');
        }

        // Check if province has districts
        $districtModel = new \App\Models\GeoDistrictModel();
        $districtCount = $districtModel->where('province_id', $id)->countAllResults();
        
        if ($districtCount > 0) {
            return redirect()->to(base_url('dakoii/geographic/provinces/country/' . $province['country_id']))
                           ->with('error', 'Cannot delete province. It has ' . $districtCount . ' districts associated with it.');
        }

        $countryId = $province['country_id'];
        
        if ($this->geoProvinceModel->delete($id)) {
            return redirect()->to(base_url('dakoii/geographic/provinces/country/' . $countryId))
                           ->with('success', 'Province deleted successfully.');
        } else {
            return redirect()->to(base_url('dakoii/geographic/provinces/country/' . $countryId))
                           ->with('error', 'Failed to delete province.');
        }
    }

    /**
     * Show CSV import form
     */
    public function import()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $countries = $this->geoCountryModel->getCountriesForDropdown();
        $selectedCountryId = $this->request->getGet('country_id');
        $selectedCountry = null;

        if ($selectedCountryId) {
            $selectedCountry = $this->geoCountryModel->find($selectedCountryId);
        }

        $data = [
            'title' => 'Import Provinces from CSV',
            'countries' => $countries,
            'selectedCountryId' => $selectedCountryId,
            'selectedCountry' => $selectedCountry
        ];

        return view('dakoii_geographic/dakoii_geographic_province_import', $data);
    }

    /**
     * Process CSV import
     */
    public function processImport()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $file = $this->request->getFile('csv_file');
        $countryId = $this->request->getPost('country_id');

        if (!$file || !$file->isValid()) {
            return redirect()->back()->with('error', 'Please select a valid CSV file.');
        }

        if (!$countryId) {
            return redirect()->back()->with('error', 'Please select a country.');
        }

        // Validate country exists
        $country = $this->geoCountryModel->find($countryId);
        if (!$country) {
            return redirect()->back()->with('error', 'Selected country not found.');
        }

        $csvData = array_map('str_getcsv', file($file->getTempName()));
        $header = array_shift($csvData);

        // Validate CSV header
        $expectedHeaders = ['province_code', 'name'];
        if (array_diff($expectedHeaders, $header)) {
            return redirect()->back()->with('error', 'CSV must have columns: province_code, name');
        }

        $imported = 0;
        $errors = [];
        $userId = $this->session->get('dakoii_user_id');

        foreach ($csvData as $rowIndex => $row) {
            if (count($row) < 2) continue;

            $data = [
                'province_code' => strtoupper(trim($row[0])),
                'name' => trim($row[1]),
                'country_id' => $countryId,
                'created_by' => $userId
            ];

            if ($this->geoProvinceModel->insert($data)) {
                $imported++;
            } else {
                $errors[] = "Row " . ($rowIndex + 2) . ": " . implode(', ', $this->geoProvinceModel->errors());
            }
        }

        $message = "Import completed. {$imported} provinces imported successfully.";
        if (!empty($errors)) {
            $message .= " " . count($errors) . " errors occurred.";
        }

        return redirect()->to(base_url('dakoii/geographic/provinces/country/' . $countryId))
                       ->with('success', $message)
                       ->with('import_errors', $errors);
    }

    /**
     * Download CSV template
     */
    public function downloadTemplate()
    {
        $type = $this->request->getGet('type') ?? 'sample';

        if ($type === 'blank') {
            $csvContent = "province_code,name\n,\n,\n,\n,\n,";
            $filename = 'provinces_blank_template.csv';
        } else {
            $csvContent = "province_code,name\n";
            $csvContent .= "NCD,National Capital District\n";
            $csvContent .= "CP,Central Province\n";
            $csvContent .= "WP,Western Province\n";
            $csvContent .= "GP,Gulf Province\n";
            $csvContent .= "MP,Morobe Province\n";
            $csvContent .= "EHP,Eastern Highlands Province\n";
            $csvContent .= "WHP,Western Highlands Province\n";
            $csvContent .= "SHP,Southern Highlands Province\n";
            $csvContent .= "EP,Enga Province\n";
            $csvContent .= "CHP,Chimbu Province";
            $filename = 'provinces_import_template.csv';
        }

        return $this->response
                    ->setHeader('Content-Type', 'text/csv')
                    ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
                    ->setBody($csvContent);
    }
}
