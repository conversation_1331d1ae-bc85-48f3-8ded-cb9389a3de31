<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-geo-alt me-2"></i>District Details
                            </h2>
                            <p class="text-light mb-0">View district information</p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/geographic/districts/province/' . $district['province_id']) ?>" class="btn btn-secondary me-2">
                                <i class="bi bi-arrow-left me-2"></i>Back to Districts
                            </a>
                            <a href="<?= base_url('dakoii/geographic/districts/' . $district['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit District
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- District Details -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-info-circle me-2"></i>District Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8 mb-4">
                            <label class="form-label text-light">District Name</label>
                            <div class="form-control-plaintext text-light fs-5">
                                <i class="bi bi-geo-alt me-2"></i><?= esc($district['name']) ?>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <label class="form-label text-light">District Code</label>
                            <div class="form-control-plaintext">
                                <?php if ($district['district_code']): ?>
                                    <span class="badge bg-warning fs-6"><?= esc($district['district_code']) ?></span>
                                <?php else: ?>
                                    <span class="text-muted">No code assigned</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label class="form-label text-light">Province</label>
                            <div class="form-control-plaintext text-light">
                                <i class="bi bi-map me-2"></i>
                                <a href="<?= base_url('dakoii/geographic/provinces/' . $district['province_id']) ?>" class="text-success">
                                    <?= esc($district['province_name']) ?>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <label class="form-label text-light">Country</label>
                            <div class="form-control-plaintext text-light">
                                <i class="bi bi-flag me-2"></i>
                                <a href="<?= base_url('dakoii/geographic/countries/' . $district['country_id']) ?>" class="text-info">
                                    <?= esc($district['country_name']) ?>
                                </a>
                                <span class="badge bg-info ms-2"><?= esc($district['country_code']) ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-light">Created</label>
                            <div class="form-control-plaintext text-muted">
                                <i class="bi bi-calendar-plus me-2"></i>
                                <?= date('F j, Y g:i A', strtotime($district['created_at'])) ?>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-light">Last Updated</label>
                            <div class="form-control-plaintext text-muted">
                                <i class="bi bi-calendar-check me-2"></i>
                                <?= date('F j, Y g:i A', strtotime($district['updated_at'])) ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Data -->
    <div class="row mt-4">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-diagram-3 me-2"></i>Geographic Hierarchy
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="p-3 border rounded">
                                <i class="bi bi-flag text-primary" style="font-size: 2rem;"></i>
                                <h6 class="text-light mt-2">Country</h6>
                                <p class="text-muted mb-2"><?= esc($district['country_name']) ?></p>
                                <a href="<?= base_url('dakoii/geographic/countries/' . $district['country_id']) ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye me-1"></i>View Country
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="p-3 border rounded">
                                <i class="bi bi-map text-success" style="font-size: 2rem;"></i>
                                <h6 class="text-light mt-2">Province</h6>
                                <p class="text-muted mb-2"><?= esc($district['province_name']) ?></p>
                                <a href="<?= base_url('dakoii/geographic/provinces/' . $district['province_id']) ?>" class="btn btn-outline-success btn-sm">
                                    <i class="bi bi-eye me-1"></i>View Province
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="p-3 border rounded bg-warning bg-opacity-10">
                                <i class="bi bi-geo-alt text-warning" style="font-size: 2rem;"></i>
                                <h6 class="text-light mt-2">District</h6>
                                <p class="text-muted mb-2"><?= esc($district['name']) ?></p>
                                <span class="badge bg-warning">Current Location</span>
                            </div>
                        </div>
                    </div>

                    <!-- Breadcrumb Navigation -->
                    <div class="mt-4">
                        <nav aria-label="Geographic breadcrumb">
                            <ol class="breadcrumb bg-secondary">
                                <li class="breadcrumb-item">
                                    <a href="<?= base_url('dakoii/geographic/countries/' . $district['country_id']) ?>" class="text-info">
                                        <i class="bi bi-flag me-1"></i><?= esc($district['country_name']) ?>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="<?= base_url('dakoii/geographic/provinces/' . $district['province_id']) ?>" class="text-success">
                                        <i class="bi bi-map me-1"></i><?= esc($district['province_name']) ?>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active text-warning" aria-current="page">
                                    <i class="bi bi-geo-alt me-1"></i><?= esc($district['name']) ?>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
