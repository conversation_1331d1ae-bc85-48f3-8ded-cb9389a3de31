<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-speedometer2 me-2"></i>Dakoii Panel Dashboard
                            </h2>
                            <p class="text-light mb-0">
                                Welcome back, <strong class="text-light"><?= esc($user['name']) ?></strong>
                                <span class="badge bg-primary ms-2"><?= esc($user['role']) ?></span>
                            </p>
                            <small class="text-muted">Organization: <?= esc($user['orgcode']) ?></small>
                        </div>
                        <div class="text-end">
                            <p class="text-muted small mb-1">Last Login</p>
                            <p class="text-light mb-0"><?= date('M d, Y H:i') ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card card-dark h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="p-3 rounded-circle bg-primary bg-opacity-20">
                                <i class="bi bi-people-fill text-primary" style="font-size: 1.5rem;"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Organizations</h6>
                            <h4 class="text-light mb-0"><?= number_format($stats['total_orgs']) ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card card-dark h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="p-3 rounded-circle bg-success bg-opacity-20">
                                <i class="bi bi-graph-up text-success" style="font-size: 1.5rem;"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Active Organizations</h6>
                            <h4 class="text-light mb-0"><?= number_format($stats['active_orgs']) ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card card-dark h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="p-3 rounded-circle bg-warning bg-opacity-20">
                                <i class="bi bi-exclamation-triangle-fill text-warning" style="font-size: 1.5rem;"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Users</h6>
                            <h4 class="text-light mb-0"><?= number_format($stats['total_users']) ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card card-dark h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="p-3 rounded-circle bg-info bg-opacity-20">
                                <i class="bi bi-server text-info" style="font-size: 1.5rem;"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Active Users</h6>
                            <h4 class="text-success mb-0"><?= number_format($stats['active_users']) ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="text-light mb-0">
                        <i class="bi bi-lightning-charge me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('dakoii/organizations/new') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3 text-light">
                                <i class="bi bi-building-add mb-2 text-primary" style="font-size: 2rem;"></i>
                                <span class="text-light">Add Organization</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('dakoii/organization-users/new') ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3 text-light">
                                <i class="bi bi-person-plus-fill mb-2 text-success" style="font-size: 2rem;"></i>
                                <span class="text-light">Add User</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('dakoii/geographic/countries') ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3 text-light">
                                <i class="bi bi-globe mb-2 text-warning" style="font-size: 2rem;"></i>
                                <span class="text-light">Geographic Data</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3 text-light">
                                <i class="bi bi-building mb-2 text-info" style="font-size: 2rem;"></i>
                                <span class="text-light">View Organizations</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="text-light mb-0">
                        <i class="bi bi-clock-history me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <?php if (!empty($recent_orgs)): ?>
                            <?php foreach ($recent_orgs as $org): ?>
                                <div class="list-group-item bg-transparent border-secondary">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 text-light">New Organization</h6>
                                        <small class="text-muted"><?= time_ago($org['created_at']) ?></small>
                                    </div>
                                    <p class="mb-1 text-muted"><?= esc($org['org_name']) ?> (<?= esc($org['org_code']) ?>) was created</p>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <?php if (!empty($recent_users)): ?>
                            <?php foreach (array_slice($recent_users, 0, 3) as $user): ?>
                                <div class="list-group-item bg-transparent border-secondary">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 text-light">New User</h6>
                                        <small class="text-muted"><?= time_ago($user['created_at']) ?></small>
                                    </div>
                                    <p class="mb-1 text-muted"><?= esc($user['name']) ?> joined <?= esc($user['org_name'] ?? 'Unknown Org') ?></p>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <?php if (empty($recent_orgs) && empty($recent_users)): ?>
                            <div class="list-group-item bg-transparent border-secondary">
                                <p class="mb-0 text-muted text-center">No recent activity</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="text-light mb-0">
                        <i class="bi bi-info-circle me-2"></i>System Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Total Organizations</span>
                            <span class="text-light"><?= number_format($stats['total_orgs']) ?></span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Active Organizations</span>
                            <span class="text-success"><?= number_format($stats['active_orgs']) ?></span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Total Users</span>
                            <span class="text-light"><?= number_format($stats['total_users']) ?></span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Active Users</span>
                            <span class="text-success"><?= number_format($stats['active_users']) ?></span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">System Status</span>
                            <span class="text-success">Online</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Auto-refresh dashboard data every 30 seconds
    setInterval(function() {
        // You can add AJAX calls here to refresh dashboard data
        console.log('Dashboard data refresh...');
    }, 30000);
</script>
<?= $this->endSection() ?>
