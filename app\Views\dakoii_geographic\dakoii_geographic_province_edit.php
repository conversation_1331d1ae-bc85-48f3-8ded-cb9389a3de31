<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-pencil me-2"></i>Edit Province
                            </h2>
                            <p class="text-light mb-0">Update province: <strong><?= esc($province['name']) ?></strong></p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/geographic/provinces/country/' . $province['country_id']) ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Provinces
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-map me-2"></i>Province Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?= base_url('dakoii/geographic/provinces/' . $province['id'] . '/update') ?>">
                        <?= csrf_field() ?>
                        
                        <!-- Country Selection -->
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="country_id" class="form-label text-light">Country <span class="text-danger">*</span></label>
                                <select class="form-select" id="country_id" name="country_id" required>
                                    <option value="">Select Country</option>
                                    <?php foreach ($countries as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= (old('country_id', $province['country_id']) == $id) ? 'selected' : '' ?>>
                                            <?= esc($name) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text text-muted">Select the country this province belongs to</div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="name" class="form-label text-light">Province Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name', $province['name']) ?>" required maxlength="100">
                                <div class="form-text text-muted">Full name of the province</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="province_code" class="form-label text-light">Province Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="province_code" name="province_code" 
                                       value="<?= old('province_code', $province['province_code']) ?>" required maxlength="10" 
                                       style="text-transform: uppercase;" placeholder="e.g., NCD">
                                <div class="form-text text-muted">Unique province code</div>
                            </div>
                        </div>

                        <!-- Metadata -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-light">Created</label>
                                <div class="form-control-plaintext text-muted">
                                    <?= date('F j, Y g:i A', strtotime($province['created_at'])) ?>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-light">Last Updated</label>
                                <div class="form-control-plaintext text-muted">
                                    <?= date('F j, Y g:i A', strtotime($province['updated_at'])) ?>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="<?= base_url('dakoii/geographic/provinces/country/' . $province['country_id']) ?>" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Update Province
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-uppercase province code input
    const provinceCodeInput = document.getElementById('province_code');
    provinceCodeInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });
});
</script>
<?= $this->endSection() ?>
