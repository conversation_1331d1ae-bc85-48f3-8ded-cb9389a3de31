<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-pencil me-2"></i>Edit District
                            </h2>
                            <p class="text-light mb-0">Update district: <strong><?= esc($district['name']) ?></strong></p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/geographic/districts/province/' . $district['province_id']) ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Districts
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-geo-alt me-2"></i>District Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i><?= session()->getFlashdata('error') ?>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?= base_url('dakoii/geographic/districts/' . $district['id'] . '/update') ?>">
                        <?= csrf_field() ?>
                        
                        <!-- Country and Province Selection -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country_id" class="form-label text-light">Country <span class="text-danger">*</span></label>
                                <select class="form-select" id="country_id" name="country_id" required onchange="loadProvinces()">
                                    <option value="">Select Country</option>
                                    <?php foreach ($countries as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= (old('country_id', $district['country_id']) == $id) ? 'selected' : '' ?>>
                                            <?= esc($name) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text text-muted">Select the country</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="province_id" class="form-label text-light">Province <span class="text-danger">*</span></label>
                                <select class="form-select" id="province_id" name="province_id" required>
                                    <option value="">Select Province</option>
                                    <?php foreach ($provinces as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= (old('province_id', $district['province_id']) == $id) ? 'selected' : '' ?>>
                                            <?= esc($name) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text text-muted">Select the province this district belongs to</div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="name" class="form-label text-light">District Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name', $district['name']) ?>" required maxlength="100">
                                <div class="form-text text-muted">Full name of the district</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="district_code" class="form-label text-light">District Code</label>
                                <input type="text" class="form-control" id="district_code" name="district_code" 
                                       value="<?= old('district_code', $district['district_code']) ?>" maxlength="10" 
                                       style="text-transform: uppercase;" placeholder="Optional">
                                <div class="form-text text-muted">Optional district code</div>
                            </div>
                        </div>

                        <!-- Metadata -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-light">Created</label>
                                <div class="form-control-plaintext text-muted">
                                    <?= date('F j, Y g:i A', strtotime($district['created_at'])) ?>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-light">Last Updated</label>
                                <div class="form-control-plaintext text-muted">
                                    <?= date('F j, Y g:i A', strtotime($district['updated_at'])) ?>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="<?= base_url('dakoii/geographic/districts/province/' . $district['province_id']) ?>" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Update District
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-uppercase district code input
    const districtCodeInput = document.getElementById('district_code');
    districtCodeInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });
});

function loadProvinces() {
    const countryId = document.getElementById('country_id').value;
    const provinceSelect = document.getElementById('province_id');
    const currentProvinceId = '<?= $district['province_id'] ?>';
    
    // Clear current options
    provinceSelect.innerHTML = '<option value="">Loading...</option>';
    
    if (!countryId) {
        provinceSelect.innerHTML = '<option value="">Select Province</option>';
        return;
    }
    
    // Make AJAX request to load provinces
    fetch('<?= base_url('dakoii/geographic/districts/get-provinces') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'country_id=' + countryId + '&<?= csrf_token() ?>=' + '<?= csrf_hash() ?>'
    })
    .then(response => response.json())
    .then(data => {
        provinceSelect.innerHTML = '<option value="">Select Province</option>';
        
        if (data.provinces) {
            Object.keys(data.provinces).forEach(id => {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = data.provinces[id];
                if (id == currentProvinceId) {
                    option.selected = true;
                }
                provinceSelect.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading provinces:', error);
        provinceSelect.innerHTML = '<option value="">Error loading provinces</option>';
    });
}
</script>
<?= $this->endSection() ?>
