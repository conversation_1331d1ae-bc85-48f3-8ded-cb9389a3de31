<?php

namespace App\Models;

use CodeIgniter\Model;

class DakoiiOrgModel extends Model
{
    protected $table = 'dakoii_org';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'org_code',
        'org_name',
        'description',
        'province_id',
        'country_id',
        'logo_path',
        'is_locationlocked',
        'postal_address',
        'phone_numbers',
        'email_addresses',
        'is_active',
        'license_status',
        'created_by',
        'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'org_code' => 'required|max_length[100]|is_unique[dakoii_org.org_code,id,{id}]',
        'org_name' => 'required|max_length[255]',
        'province_id' => 'permit_empty|integer',
        'country_id' => 'permit_empty|integer',
        'logo_path' => 'permit_empty|max_length[255]',
        'is_locationlocked' => 'in_list[0,1]',
        'is_active' => 'in_list[0,1]',
        'license_status' => 'permit_empty|max_length[50]'
    ];
    
    protected $validationMessages = [
        'org_code' => [
            'required' => 'Organization code is required',
            'max_length' => 'Organization code cannot exceed 100 characters',
            'is_unique' => 'Organization code already exists'
        ],
        'org_name' => [
            'required' => 'Organization name is required',
            'max_length' => 'Organization name cannot exceed 255 characters'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Get active organizations
     */
    public function getActiveOrgs()
    {
        return $this->where('is_active', 1)
                   ->where('deleted_at', null)
                   ->findAll();
    }
    
    /**
     * Get organization by code
     */
    public function getByOrgCode(string $orgCode)
    {
        return $this->where('org_code', $orgCode)
                   ->where('deleted_at', null)
                   ->first();
    }
    
    /**
     * Get organizations with country and province info
     */
    public function getOrgsWithLocation()
    {
        return $this->select('dakoii_org.*, geo_countries.name as country_name, geo_provinces.name as province_name')
                   ->join('geo_countries', 'geo_countries.id = dakoii_org.country_id', 'left')
                   ->join('geo_provinces', 'geo_provinces.id = dakoii_org.province_id', 'left')
                   ->where('dakoii_org.deleted_at', null)
                   ->findAll();
    }
    
    /**
     * Get organization statistics
     */
    public function getOrgStats()
    {
        $stats = [];
        
        // Total organizations
        $stats['total'] = $this->where('deleted_at', null)->countAllResults();
        
        // Active organizations
        $stats['active'] = $this->where('is_active', 1)
                               ->where('deleted_at', null)
                               ->countAllResults();
        
        // Inactive organizations
        $stats['inactive'] = $this->where('is_active', 0)
                                 ->where('deleted_at', null)
                                 ->countAllResults();
        
        return $stats;
    }
    
    /**
     * Search organizations
     */
    public function searchOrgs(string $keyword)
    {
        return $this->groupStart()
                       ->like('org_code', $keyword)
                       ->orLike('org_name', $keyword)
                       ->orLike('description', $keyword)
                   ->groupEnd()
                   ->where('deleted_at', null)
                   ->findAll();
    }

    /**
     * Handle logo file upload
     */
    public function handleLogoUpload($logoFile)
    {
        if (!$logoFile || !$logoFile->isValid() || $logoFile->hasMoved()) {
            return null;
        }

        // Ensure upload directory exists
        $uploadPath = FCPATH . 'uploads/org_logos/';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Generate unique filename
        $newName = $logoFile->getRandomName();

        // Move file to upload directory
        if ($logoFile->move($uploadPath, $newName)) {
            // Return path with public/ prefix for web access
            return 'public/uploads/org_logos/' . $newName;
        }

        return null;
    }

    /**
     * Delete old logo file
     */
    public function deleteOldLogo($logoPath)
    {
        if (empty($logoPath)) {
            return true;
        }

        // Remove public/ prefix to get actual file path
        $filePath = str_replace('public/', FCPATH, $logoPath);

        if (file_exists($filePath)) {
            return unlink($filePath);
        }

        return true;
    }
}
