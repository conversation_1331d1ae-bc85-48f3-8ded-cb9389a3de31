<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-map me-2"></i>Provinces Management
                                <?php if (isset($selectedCountry)): ?>
                                    <small class="text-muted">in <?= esc($selectedCountry['name']) ?></small>
                                <?php endif; ?>
                            </h2>
                            <p class="text-light mb-0">Manage geographic provinces and their information</p>
                        </div>
                        <div>
                            <?php if (isset($selectedCountryId)): ?>
                                <a href="<?= base_url('dakoii/geographic/provinces/new/' . $selectedCountryId) ?>" class="btn btn-primary me-2">
                                    <i class="bi bi-map-fill me-2"></i>Add Province to <?= esc($selectedCountry['name']) ?>
                                </a>
                                <div class="btn-group me-2" role="group">
                                    <a href="<?= base_url('dakoii/geographic/provinces/import?country_id=' . $selectedCountryId) ?>" class="btn btn-success">
                                        <i class="bi bi-upload me-2"></i>Import CSV
                                    </a>
                                    <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item text-dark" href="<?= base_url('dakoii/geographic/provinces/download-template?type=sample') ?>">
                                            <i class="bi bi-download me-2"></i>Download Sample Template
                                        </a></li>
                                        <li><a class="dropdown-item text-dark" href="<?= base_url('dakoii/geographic/provinces/download-template?type=blank') ?>">
                                            <i class="bi bi-file-earmark me-2"></i>Download Blank Template
                                        </a></li>
                                    </ul>
                                </div>
                                <a href="<?= base_url('dakoii/geographic/countries/' . $selectedCountryId) ?>" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Country
                                </a>
                            <?php else: ?>
                                <a href="<?= base_url('dakoii/geographic/provinces/new') ?>" class="btn btn-primary me-2">
                                    <i class="bi bi-map-fill me-2"></i>Add New Province
                                </a>
                                <div class="btn-group" role="group">
                                    <a href="<?= base_url('dakoii/geographic/provinces/import') ?>" class="btn btn-success">
                                        <i class="bi bi-upload me-2"></i>Import CSV
                                    </a>
                                    <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item text-dark" href="<?= base_url('dakoii/geographic/provinces/download-template?type=sample') ?>">
                                            <i class="bi bi-download me-2"></i>Download Sample Template
                                        </a></li>
                                        <li><a class="dropdown-item text-dark" href="<?= base_url('dakoii/geographic/provinces/download-template?type=blank') ?>">
                                            <i class="bi bi-file-earmark me-2"></i>Download Blank Template
                                        </a></li>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i><?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('import_errors')): ?>
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <h6><i class="bi bi-exclamation-triangle me-2"></i>Import Errors:</h6>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('import_errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Country Filter (only show if not filtered by country) -->
    <?php if (!isset($selectedCountryId)): ?>
        <div class="row mb-3">
            <div class="col-12">
                <div class="card card-dark">
                    <div class="card-body">
                        <form method="get" action="<?= base_url('dakoii/geographic/provinces') ?>">
                            <div class="row align-items-end">
                                <div class="col-md-4">
                                    <label for="country_filter" class="form-label text-light">Filter by Country</label>
                                    <select class="form-select" id="country_filter" name="country_id" onchange="filterByCountry()">
                                        <option value="">All Countries</option>
                                        <?php foreach ($countries as $id => $name): ?>
                                            <option value="<?= $id ?>"><?= esc($name) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Provinces Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-list me-2"></i>Provinces List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($provinces)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-map text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Provinces Found</h4>
                            <?php if (isset($selectedCountryId)): ?>
                                <p class="text-muted">Start by adding provinces to <?= esc($selectedCountry['name']) ?>.</p>
                                <a href="<?= base_url('dakoii/geographic/provinces/new/' . $selectedCountryId) ?>" class="btn btn-primary">
                                    <i class="bi bi-map-fill me-2"></i>Add Province
                                </a>
                            <?php else: ?>
                                <p class="text-muted">Start by adding your first province.</p>
                                <a href="<?= base_url('dakoii/geographic/provinces/new') ?>" class="btn btn-primary">
                                    <i class="bi bi-map-fill me-2"></i>Add Province
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Province</th>
                                        <th>Province Code</th>
                                        <?php if (!isset($selectedCountryId)): ?>
                                            <th>Country</th>
                                        <?php endif; ?>
                                        <th>Districts</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($provinces as $index => $province): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-success rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="bi bi-map text-white"></i>
                                                    </div>
                                                    <div>
                                                        <strong><?= esc($province['name']) ?></strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-success"><?= esc($province['province_code']) ?></span>
                                            </td>
                                            <?php if (!isset($selectedCountryId)): ?>
                                                <td>
                                                    <a href="<?= base_url('dakoii/geographic/provinces/country/' . $province['country_id']) ?>" class="text-info">
                                                        <?= esc($province['country_name'] ?? 'Unknown') ?>
                                                    </a>
                                                </td>
                                            <?php endif; ?>
                                            <td>
                                                <a href="<?= base_url('dakoii/geographic/districts/province/' . $province['id']) ?>" class="btn btn-sm btn-outline-warning">
                                                    <i class="bi bi-geo-alt me-1"></i>View Districts
                                                </a>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M j, Y', strtotime($province['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('dakoii/geographic/provinces/' . $province['id']) ?>" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('dakoii/geographic/provinces/' . $province['id'] . '/edit') ?>" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(<?= $province['id'] ?>, '<?= esc($province['name']) ?>', <?= $province['country_id'] ?>)" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title text-light">Confirm Delete</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <p>Are you sure you want to delete the province <strong id="provinceName"></strong>?</p>
                <p class="text-warning"><i class="bi bi-exclamation-triangle me-2"></i>This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">Delete Province</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(provinceId, provinceName, countryId) {
    document.getElementById('provinceName').textContent = provinceName;
    document.getElementById('deleteForm').action = '<?= base_url('dakoii/geographic/provinces/') ?>' + provinceId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function filterByCountry() {
    const countryId = document.getElementById('country_filter').value;
    if (countryId) {
        window.location.href = '<?= base_url('dakoii/geographic/provinces/country/') ?>' + countryId;
    } else {
        window.location.href = '<?= base_url('dakoii/geographic/provinces') ?>';
    }
}
</script>
<?= $this->endSection() ?>
