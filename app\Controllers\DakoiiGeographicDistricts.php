<?php

namespace App\Controllers;

use App\Models\GeoDistrictModel;
use App\Models\GeoProvinceModel;
use App\Models\GeoCountryModel;
use CodeIgniter\Controller;

class DakoiiGeographicDistricts extends Controller
{
    protected $geoDistrictModel;
    protected $geoProvinceModel;
    protected $geoCountryModel;
    protected $session;

    public function __construct()
    {
        $this->geoDistrictModel = new GeoDistrictModel();
        $this->geoProvinceModel = new GeoProvinceModel();
        $this->geoCountryModel = new GeoCountryModel();
        $this->session = session();
    }

    /**
     * Check authentication
     */
    private function checkAuth()
    {
        if (!$this->session->get('dakoii_logged_in')) {
            return redirect()->to(base_url('dakoii'))->with('error', 'Please login to access this page.');
        }
        return null;
    }

    /**
     * Display list of districts
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $countryId = $this->request->getGet('country_id');
        $districts = $this->geoDistrictModel->getDistrictsWithDetails();
        
        // Filter by country if specified
        if ($countryId) {
            $districts = array_filter($districts, function($district) use ($countryId) {
                return $district['country_id'] == $countryId;
            });
        }

        $countries = $this->geoCountryModel->getCountriesForDropdown();

        $data = [
            'title' => 'Districts Management',
            'districts' => $districts,
            'countries' => $countries,
            'selectedCountryId' => $countryId
        ];

        return view('dakoii_geographic/dakoii_geographic_district_list', $data);
    }

    /**
     * Display districts by province
     */
    public function byProvince($provinceId)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $province = $this->geoProvinceModel->select('geo_provinces.*, geo_countries.name as country_name')
                                          ->join('geo_countries', 'geo_countries.id = geo_provinces.country_id', 'left')
                                          ->where('geo_provinces.id', $provinceId)
                                          ->first();
        
        if (!$province) {
            return redirect()->to(base_url('dakoii/geographic/districts'))
                           ->with('error', 'Province not found.');
        }

        $districts = $this->geoDistrictModel->getDistrictsByProvince($provinceId);
        $countries = $this->geoCountryModel->getCountriesForDropdown();

        $data = [
            'title' => 'Districts in ' . $province['name'],
            'districts' => $districts,
            'countries' => $countries,
            'selectedProvince' => $province,
            'selectedProvinceId' => $provinceId
        ];

        return view('dakoii_geographic/dakoii_geographic_district_list', $data);
    }

    /**
     * Show create form
     */
    public function new($provinceId = null)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $countries = $this->geoCountryModel->getCountriesForDropdown();
        $provinces = [];
        $selectedProvince = null;
        $selectedCountryId = null;

        if ($provinceId) {
            $selectedProvince = $this->geoProvinceModel->select('geo_provinces.*, geo_countries.name as country_name')
                                                      ->join('geo_countries', 'geo_countries.id = geo_provinces.country_id', 'left')
                                                      ->where('geo_provinces.id', $provinceId)
                                                      ->first();
            if (!$selectedProvince) {
                return redirect()->to(base_url('dakoii/geographic/districts/new'))
                               ->with('error', 'Province not found.');
            }
            $selectedCountryId = $selectedProvince['country_id'];
            $provinces = $this->geoProvinceModel->getProvincesForDropdown($selectedCountryId);
        }

        $data = [
            'title' => 'Add New District',
            'district' => [],
            'countries' => $countries,
            'provinces' => $provinces,
            'selectedProvinceId' => $provinceId,
            'selectedProvince' => $selectedProvince,
            'selectedCountryId' => $selectedCountryId
        ];

        return view('dakoii_geographic/dakoii_geographic_district_create', $data);
    }

    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $provinceId = $this->request->getPost('province_id');
        $province = $this->geoProvinceModel->find($provinceId);
        
        if (!$province) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Selected province not found.');
        }

        $data = [
            'district_code' => $this->request->getPost('district_code') ? strtoupper($this->request->getPost('district_code')) : null,
            'name' => $this->request->getPost('name'),
            'country_id' => $province['country_id'],
            'province_id' => $provinceId,
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->geoDistrictModel->insert($data)) {
            return redirect()->to(base_url('dakoii/geographic/districts/province/' . $provinceId))
                           ->with('success', 'District created successfully.');
        } else {
            return redirect()->back()
                           ->withInput()
                           ->with('errors', $this->geoDistrictModel->errors());
        }
    }

    /**
     * Show district details
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $district = $this->geoDistrictModel->getDistrictWithDetails($id);
        
        if (!$district) {
            return redirect()->to(base_url('dakoii/geographic/districts'))
                           ->with('error', 'District not found.');
        }

        $data = [
            'title' => 'District Details',
            'district' => $district
        ];

        return view('dakoii_geographic/dakoii_geographic_district_show', $data);
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $district = $this->geoDistrictModel->find($id);
        
        if (!$district) {
            return redirect()->to(base_url('dakoii/geographic/districts'))
                           ->with('error', 'District not found.');
        }

        $countries = $this->geoCountryModel->getCountriesForDropdown();
        $provinces = $this->geoProvinceModel->getProvincesForDropdown($district['country_id']);

        $data = [
            'title' => 'Edit District',
            'district' => $district,
            'countries' => $countries,
            'provinces' => $provinces
        ];

        return view('dakoii_geographic/dakoii_geographic_district_edit', $data);
    }

    /**
     * Handle update form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $district = $this->geoDistrictModel->find($id);
        
        if (!$district) {
            return redirect()->to(base_url('dakoii/geographic/districts'))
                           ->with('error', 'District not found.');
        }

        $provinceId = $this->request->getPost('province_id');
        $province = $this->geoProvinceModel->find($provinceId);
        
        if (!$province) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Selected province not found.');
        }

        $data = [
            'district_code' => $this->request->getPost('district_code') ? strtoupper($this->request->getPost('district_code')) : null,
            'name' => $this->request->getPost('name'),
            'country_id' => $province['country_id'],
            'province_id' => $provinceId,
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->geoDistrictModel->update($id, $data)) {
            return redirect()->to(base_url('dakoii/geographic/districts/province/' . $provinceId))
                           ->with('success', 'District updated successfully.');
        } else {
            return redirect()->back()
                           ->withInput()
                           ->with('errors', $this->geoDistrictModel->errors());
        }
    }

    /**
     * Handle delete
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $district = $this->geoDistrictModel->find($id);
        
        if (!$district) {
            return redirect()->to(base_url('dakoii/geographic/districts'))
                           ->with('error', 'District not found.');
        }

        $provinceId = $district['province_id'];
        
        if ($this->geoDistrictModel->delete($id)) {
            return redirect()->to(base_url('dakoii/geographic/districts/province/' . $provinceId))
                           ->with('success', 'District deleted successfully.');
        } else {
            return redirect()->to(base_url('dakoii/geographic/districts/province/' . $provinceId))
                           ->with('error', 'Failed to delete district.');
        }
    }

    /**
     * Show CSV import form
     */
    public function import()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $countries = $this->geoCountryModel->getCountriesForDropdown();
        $selectedProvinceId = $this->request->getGet('province_id');
        $selectedProvince = null;
        $selectedCountryId = null;
        $provinces = [];

        if ($selectedProvinceId) {
            $selectedProvince = $this->geoProvinceModel->select('geo_provinces.*, geo_countries.name as country_name')
                                                      ->join('geo_countries', 'geo_countries.id = geo_provinces.country_id', 'left')
                                                      ->where('geo_provinces.id', $selectedProvinceId)
                                                      ->first();
            if ($selectedProvince) {
                $selectedCountryId = $selectedProvince['country_id'];
                $provinces = $this->geoProvinceModel->getProvincesForDropdown($selectedCountryId);
            }
        }

        $data = [
            'title' => 'Import Districts from CSV',
            'countries' => $countries,
            'provinces' => $provinces,
            'selectedProvinceId' => $selectedProvinceId,
            'selectedProvince' => $selectedProvince,
            'selectedCountryId' => $selectedCountryId
        ];

        return view('dakoii_geographic/dakoii_geographic_district_import', $data);
    }

    /**
     * Process CSV import
     */
    public function processImport()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $file = $this->request->getFile('csv_file');
        $provinceId = $this->request->getPost('province_id');

        if (!$file || !$file->isValid()) {
            return redirect()->back()->with('error', 'Please select a valid CSV file.');
        }

        if (!$provinceId) {
            return redirect()->back()->with('error', 'Please select a province.');
        }

        // Validate province exists
        $province = $this->geoProvinceModel->find($provinceId);
        if (!$province) {
            return redirect()->back()->with('error', 'Selected province not found.');
        }

        $csvData = array_map('str_getcsv', file($file->getTempName()));
        $header = array_shift($csvData);

        // Validate CSV header
        $expectedHeaders = ['district_code', 'name'];
        if (array_diff($expectedHeaders, $header)) {
            return redirect()->back()->with('error', 'CSV must have columns: district_code, name');
        }

        $imported = 0;
        $errors = [];
        $userId = $this->session->get('dakoii_user_id');

        foreach ($csvData as $rowIndex => $row) {
            if (count($row) < 2) continue;

            $data = [
                'district_code' => trim($row[0]) ? strtoupper(trim($row[0])) : null,
                'name' => trim($row[1]),
                'country_id' => $province['country_id'],
                'province_id' => $provinceId,
                'created_by' => $userId
            ];

            if ($this->geoDistrictModel->insert($data)) {
                $imported++;
            } else {
                $errors[] = "Row " . ($rowIndex + 2) . ": " . implode(', ', $this->geoDistrictModel->errors());
            }
        }

        $message = "Import completed. {$imported} districts imported successfully.";
        if (!empty($errors)) {
            $message .= " " . count($errors) . " errors occurred.";
        }

        return redirect()->to(base_url('dakoii/geographic/districts/province/' . $provinceId))
                       ->with('success', $message)
                       ->with('import_errors', $errors);
    }

    /**
     * Get provinces by country (AJAX helper)
     */
    public function getProvincesByCountry()
    {
        $countryId = $this->request->getPost('country_id');

        if (!$countryId) {
            return $this->response->setJSON(['provinces' => []]);
        }

        try {
            $provinces = $this->geoProvinceModel->getProvincesForDropdown($countryId);
            return $this->response->setJSON(['provinces' => $provinces]);
        } catch (\Exception $e) {
            log_message('error', 'Error loading provinces: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Error loading provinces']);
        }
    }

    /**
     * Download CSV template
     */
    public function downloadTemplate()
    {
        $type = $this->request->getGet('type') ?? 'sample';

        if ($type === 'blank') {
            $csvContent = "district_code,name\n,\n,\n,\n,\n,";
            $filename = 'districts_blank_template.csv';
        } else {
            $csvContent = "district_code,name\n";
            $csvContent .= "POM,Port Moresby\n";
            $csvContent .= "LAE,Lae Urban\n";
            $csvContent .= "MTH,Mount Hagen\n";
            $csvContent .= "VAN,Vanimo\n";
            $csvContent .= "MAD,Madang\n";
            $csvContent .= "KER,Kerema\n";
            $csvContent .= "DRU,Daru\n";
            $csvContent .= "MEN,Mendi\n";
            $csvContent .= "WAB,Wabag\n";
            $csvContent .= "KUN,Kundiawa";
            $filename = 'districts_import_template.csv';
        }

        return $this->response
                    ->setHeader('Content-Type', 'text/csv')
                    ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
                    ->setBody($csvContent);
    }
}
